import { Image } from "react-native";
import { useFocusEffect, useRouter } from "expo-router";
import {
  Container,
  Heading,
  Question,
  LinkText,
  ContentContainer,
  LinkContainer,
  LinkButton,
  TopContainer,
  BottomContentContainer,
  TopContentContainer,
  ButtonContainer,
  ButtonText,
} from "@/styles/Intro.styles";
import { useTheme } from "@/hooks/useTheme";
import { useTranslation } from "react-i18next";
import { useAppDispatch } from "@/store/store";
import { resetUser, setUser } from "@/store/slices/authSlice";
import { UserType } from "@/types/api";
import { useCallback } from "react";
import { Spacer } from "@/styles/common.style";
import { Text } from "@/components";

export default function IntroScreen() {
  const router = useRouter();
  const { theme } = useTheme();
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  useFocusEffect(
    useCallback(() => {
      dispatch(resetUser());
    }, [])
  );
  const handleButtonPress = (role_id: number) => {
    dispatch(setUser({ role_id }));
    router.push("/(auth)/register");
  };

  return (
    <Container contentContainerStyle={{ flex: 1 }}>
      <TopContainer>
        <Spacer size={175} />
        <Image
          source={require("../../assets/icon.png")}
          style={{ height: "80%", width: "100%" }}
          resizeMode="contain"
        />
      </TopContainer>
      <ContentContainer>
        <TopContentContainer>
          <Heading>{t("intro.greeting")}</Heading>
          <Question>{t("intro.question")}</Question>
        </TopContentContainer>

        <LinkContainer>


          <ButtonText variant="subtitle" >{t("intro.choose_role")} :</ButtonText>

          <ButtonContainer
            variant="primary"
            onPress={() => handleButtonPress(UserType.VENDOR)}

            title={t("intro.dealer_button")}
          />
          <ButtonContainer
            variant="outline"
            onPress={() => handleButtonPress(UserType.CUSTOMER)}

            title={t("intro.customer_button")}
          />
          <LinkButton onPress={() => router.push("/(auth)/login")}>
            <LinkText>{t("intro.have_account")}</LinkText>
          </LinkButton>
        </LinkContainer>
      </ContentContainer>
    </Container>
  );
}
