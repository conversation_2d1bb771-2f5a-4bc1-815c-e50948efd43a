import * as React from "react"
import Svg, { SvgProps, Mask, Path, G } from "react-native-svg"
const SearchIcon = ({ color, ...props }: SvgProps) => (
    <Svg
        width={24}
        height={24}
        fill="none"
        {...props}
    >
        <Mask
            id="a"
            width={24}
            height={24}
            x={0}
            y={0}
            maskUnits="userSpaceOnUse"
            style={{
                maskType: "alpha",
            }}
        >
            <Path fill={color || "#D9D9D9"} d="M0 0h24v24H0z" />
        </Mask>
        <G mask="url(#a)">
            <Path
                fill={color || "#A8ACB3"}
                d="M10.999 18.048c1.946 0 3.607-.684 4.984-2.051 1.377-1.368 2.065-3.033 2.065-4.996 0-1.946-.688-3.607-2.064-4.984-1.375-1.377-3.036-2.065-4.982-2.065-1.963 0-3.629.688-4.997 2.064-1.369 1.375-2.053 3.036-2.053 4.982 0 1.963.684 3.63 2.051 4.998 1.368 1.368 3.033 2.052 4.996 2.052ZM11 20.203a8.907 8.907 0 0 1-3.59-.727 9.354 9.354 0 0 1-2.916-1.97 9.353 9.353 0 0 1-1.97-2.916A8.91 8.91 0 0 1 1.797 11c0-1.261.242-2.45.727-3.568a9.462 9.462 0 0 1 1.97-2.929A9.272 9.272 0 0 1 7.41 2.524 8.91 8.91 0 0 1 11 1.797c1.261 0 2.45.242 3.567.728a9.408 9.408 0 0 1 2.928 1.979 9.365 9.365 0 0 1 1.98 2.927A8.863 8.863 0 0 1 20.204 11a8.895 8.895 0 0 1-.528 3.073 9.639 9.639 0 0 1-1.459 2.642l3.228 3.227c.216.216.321.467.317.754a1.058 1.058 0 0 1-.33.753 1.071 1.071 0 0 1-.758.305 1.07 1.07 0 0 1-.756-.305l-3.233-3.208a9.374 9.374 0 0 1-2.64 1.447 8.997 8.997 0 0 1-3.044.515Z"
            />
        </G>
    </Svg>
)
export default SearchIcon
