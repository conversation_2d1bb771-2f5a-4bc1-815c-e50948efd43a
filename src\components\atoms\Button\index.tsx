import React from "react";
import { ActivityIndicator, ViewStyle, TextStyle } from "react-native";
import {
  But<PERSON><PERSON>ontaine<PERSON>,
  ButtonText,
  Disabled<PERSON><PERSON>on,
  ButtonContent,
  StyledActivityIndicator,
} from "./styles";
import { useTheme } from "@/hooks/useTheme";

interface ButtonProps {
  onPress: () => void;
  title: string;
  loading?: boolean;
  disabled?: boolean;
  variant?: "primary" | "secondary" | "outline";
  style?: ViewStyle;
  textStyle?: TextStyle;
  size?: "small" | "medium" | "large";
  leftIcon?: React.ReactNode;
}

export default function Button({
  onPress,
  title,
  loading = false,
  disabled = false,
  variant = "primary",
  style,
  textStyle,
  size = "medium",
}: ButtonProps) {
  const ButtonComponent = disabled ? DisabledButton : ButtonContainer;
  const { theme } = useTheme();
  return (
    <ButtonComponent
      variant={variant}
      size={size}
      onPress={onPress}
      disabled={disabled || loading}
      style={style}
    >
      <ButtonContent>
        {loading && (
          <StyledActivityIndicator
            color={
              variant === "outline" ? theme.colors.primary : theme.colors.white
            }
          />
        )}
        <ButtonText disabled={disabled} variant={variant} size={size} style={textStyle}>
          {title}
        </ButtonText>
      </ButtonContent>
    </ButtonComponent>
  );
}
