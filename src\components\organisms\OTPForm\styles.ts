import { styled } from "@/utils/styled";
import { View, Text, TextInput, Pressable } from "react-native";

export const Container = styled(View)`
  padding: 24px;
  background-color: ${({ theme }) => theme.colors.background};
  border-top-left-radius: 24px;
  border-top-right-radius: 24px;
  align-items: center;
`;

export const OTPTitle = styled(Text)`
  font-size: 24px;
  font-weight: 700;
  color: ${({ theme }) => theme.colors.text};
  text-align: center;
  margin-bottom: 32px;
`;

export const OTPContainer = styled(View)`
  flex-direction: row;
  justify-content: space-between;
  margin-bottom: 32px;
  gap: 12px;
`;

export const OTPInput = styled(TextInput) <{ focused?: boolean }>`
  width: 50px;
  height: 55px;
  border-radius: 8px;
  border: 2px solid
    ${({ theme, focused }) =>
    focused ? theme.colors.primary : theme.colors.inputBackground};
  background-color: ${({ theme }) =>
    theme.colors.inputBackground || theme.colors.card};
  color: ${({ theme }) => theme.colors.inputText};
  font-size: 20px;
  font-weight: 600;
  text-align: center;
`;

export const ResendRow = styled(View)`
  flex-direction: row;
  align-items: center;
  margin-bottom: 32px;
  gap: 4px;
`;

export const ResendText = styled(Text)`
  color: ${({ theme }) => theme.colors.text};
  font-size: 14px;
`;

export const ResendLink = styled(Text)`
  color: ${({ theme }) => theme.colors.primary};
  font-size: 14px;
  font-weight: 600;
`;

export const TimerText = styled(Text)`
  color: ${({ theme }) => theme.colors.primary};
  font-size: 14px;
  font-weight: 600;
`;

export const BackButton = styled(Pressable)`
  margin-top: 16px;
  padding: 12px 24px;
  width: 100%;
`;

export const BackButtonText = styled(Text)`
  color: ${({ theme }) => theme.colors.text};
  font-size: 16px;
  text-align: center;
`;
