import * as React from "react";
import Svg, { G, Mask, Path, SvgProps } from "react-native-svg";

const InvoiceIcon = ({ color, ...props }: SvgProps) => (
  <Svg
    width={30}
    height={30}
    viewBox="0 0 30 30"
    fill="none"
    {...props}
  >
    <Mask
      id="a"
      width={30}
      height={30}
      x={0}
      y={0}
      maskUnits="userSpaceOnUse"
      style={{
        maskType: "alpha",
      }}
    >
      <Path fill={color || "#D9D9D9"} d="M0 0h30v30H0z" />
    </Mask>
    <G mask="url(#a)">
    <Path
      d="M16.875 12.1875C16.875 11.9389 16.7762 11.7004 16.6004 11.5246C16.4246 11.3488 16.1861 11.25 15.9375 11.25H8.4375C8.18886 11.25 7.9504 11.3488 7.77459 11.5246C7.59877 11.7004 7.5 11.9389 7.5 12.1875C7.5 12.4361 7.59877 12.6746 7.77459 12.8504C7.9504 13.0262 8.18886 13.125 8.4375 13.125H15.9375C16.1861 13.125 16.4246 13.0262 16.6004 12.8504C16.7762 12.6746 16.875 12.4361 16.875 12.1875ZM15.625 15.9375C15.625 15.6889 15.5262 15.4504 15.3504 15.2746C15.1746 15.0988 14.9361 15 14.6875 15H8.4375C8.18886 15 7.9504 15.0988 7.77459 15.2746C7.59877 15.4504 7.5 15.6889 7.5 15.9375C7.5 16.1861 7.59877 16.4246 7.77459 16.6004C7.9504 16.7762 8.18886 16.875 8.4375 16.875H14.6875C14.9361 16.875 15.1746 16.7762 15.3504 16.6004C15.5262 16.4246 15.625 16.1861 15.625 15.9375ZM15.9375 18.75C16.1861 18.75 16.4246 18.8488 16.6004 19.0246C16.7762 19.2004 16.875 19.4389 16.875 19.6875C16.875 19.9361 16.7762 20.1746 16.6004 20.3504C16.4246 20.5262 16.1861 20.625 15.9375 20.625H8.4375C8.18886 20.625 7.9504 20.5262 7.77459 20.3504C7.59877 20.1746 7.5 19.9361 7.5 19.6875C7.5 19.4389 7.59877 19.2004 7.77459 19.0246C7.9504 18.8488 8.18886 18.75 8.4375 18.75H15.9375Z"
      fill="#5892FF"
    />
    </G>
  </Svg>
);
export default InvoiceIcon;

