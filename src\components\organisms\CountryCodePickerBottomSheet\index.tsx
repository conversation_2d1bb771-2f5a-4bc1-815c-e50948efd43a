import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { BottomSheetModal, BottomSheetView } from "@gorhom/bottom-sheet";
import { CountryCode } from "@/types/auth";
import LoadingOverlay from "@/components/atoms/LoadingOverlay";
import {
  SearchInput,
  SearchInputContainer,
  ListContainer,
  CountryItem,
  CountryName,
  CountryCode as StyledCountryCode,
  FlagContainer,
  Title,
  FlagPlaceholderImage,
  StyledBottomSheetView,
  CountryItemContainer,
  CrossIconContainer,
  RenderListContainer,
} from "./styles";
import { useDebounce } from "@/utils/useDebounce";
import { useBottomSheetBackdrop } from "@/utils/commonBackdrop";
import { useTheme } from "@/hooks/useTheme";
import SearchIcon from "@/components/atoms/IllustratorIcon/SearchIcon";
import { RadioButton, RadioButtonInner } from "../SortBottomSheet/styles";
import { View } from "react-native";
import { Entypo } from "@expo/vector-icons";
import CrossIcon from "@/components/atoms/IllustratorIcon/CrossIcon";

interface Props {
  isVisible: boolean;
  onClose: () => void;
  onSelect: (item: CountryCode) => void;
  data: CountryCode[];
  title?: string;
  searchPlaceholder?: string;
  isLoading?: boolean;
  selectedCountry?: CountryCode;
}

const CountryCodePickerBottomSheet: React.FC<Props> = ({
  isVisible,
  onClose,
  onSelect,
  data,
  title = "Select Country Code",
  searchPlaceholder = "Search by country or code",
  isLoading = false,
  selectedCountry,
}) => {
  const sheetRef = useRef<BottomSheetModal>(null);
  const { theme } = useTheme();
  const [query, setQuery] = useState("");
  const debouceSearch = useDebounce(query, 300);
  const [filtered, setFiltered] = useState<CountryCode[]>(data);

  useEffect(() => {
    if (isVisible) {
      sheetRef.current?.present();
    } else {
      sheetRef.current?.dismiss();
    }
  }, [isVisible]);

  useEffect(() => {
    if (!debouceSearch) {
      setFiltered(data);
      return;
    }
    setFiltered(
      data.filter(
        (item) =>
          item.full_name.toLowerCase().includes(debouceSearch.toLowerCase()) ||
          item.dialling_code.includes(debouceSearch) ||
          item.country_code_alpha
            .toLowerCase()
            .includes(debouceSearch.toLowerCase())
      )
    );
  }, [debouceSearch, data]);

  const handleDismiss = useCallback(() => {
    onClose?.();
  }, [onClose]);

  const snapPoints = useMemo(() => ["100%"], []);

  const renderBackdrop = useBottomSheetBackdrop();

  return (
    <BottomSheetModal
      ref={sheetRef}
      snapPoints={snapPoints}
      onDismiss={handleDismiss}
      enablePanDownToClose
      backdropComponent={renderBackdrop}
      backgroundStyle={{ backgroundColor: theme.colors.background }}
      handleIndicatorStyle={{ backgroundColor: theme.colors.gray }}
    >
      <StyledBottomSheetView >
        <CountryItemContainer>
          <Title>{title}</Title>
          <CrossIconContainer onPress={onClose}>
            <CrossIcon color={theme.colors.text} />
          </CrossIconContainer>
        </CountryItemContainer>
        <SearchInputContainer>
          <SearchIcon color={theme.colors.inputColor} />
          <SearchInput
            placeholder={searchPlaceholder}
            placeholderTextColor={theme.colors.inputColor}
            style={{ color: theme.colors.inputColor }}
            value={query}
            onChangeText={setQuery}
          />
        </SearchInputContainer>
        <ListContainer
          data={filtered}
          keyExtractor={(item) => item.id.toString()}
          ListEmptyComponent={
            isLoading && <LoadingOverlay isLoading={isLoading} size="large" />
          }
          renderItem={({ item }) => {
            const isSelected = selectedCountry?.id === item.id;
            return (
              <CountryItem
                onPress={() => {
                  onSelect(item);
                  onClose();
                }}
              >
                <RenderListContainer>
                  <FlagContainer>
                    <FlagPlaceholderImage source={{ uri: item.flag }} />
                  </FlagContainer>
                  <CountryName>+{item.dialling_code}</CountryName>
                  <StyledCountryCode>{item.full_name}</StyledCountryCode>
                </RenderListContainer>
                <RadioButton selected={isSelected}>
                  <RadioButtonInner selected={isSelected} />
                </RadioButton>
              </CountryItem>
            );
          }}
          keyboardShouldPersistTaps="handled"
          contentContainerStyle={{ paddingBottom: 20 }}
        />
      </StyledBottomSheetView>
    </BottomSheetModal>
  );
};

export default CountryCodePickerBottomSheet;
