import Button from "../components/atoms/Button";
import { styled } from "../utils/styled";
import { Pressable, ScrollView, Text, View } from "react-native";

export const Container = styled(ScrollView)`
  flex: 1;
  background-color: ${({ theme }) => theme.colors.card};
  display: flex;
  flex-direction: column;
  
`;

export const ContentContainer = styled(View)`
  flex: 1;
  align-items: center;
  

`;

export const TopContainer = styled.View`
  flex: 1;
  align-items: center;
  justify-content: center;
  padding: 24px;
  max-height: 200px;
  margin-top: "auto";
`;
export const TopContentContainer = styled(View)`
  flex: 1;
  justify-content: center;
  width: 100%;
  align-items: center;
  gap: 24px;
  padding: 24px;
`;

export const BottomContentContainer = styled(View)`
  flex: 1;
  align-items: center;
  justify-content: center;
  gap: 24px;
  width: 100%;
`;

export const Heading = styled(Text)`
  color: ${({ theme }) => theme.colors.statusEstimation};
  font-weight: bold;
  font-size: 32px;
  text-align: center;
`;

export const Question = styled(Text)`
  color: ${({ theme }) => theme.colors.text};
  font-size: 18px;
  text-align: center;
  font-weight: 500;
  line-height: 30px;
`;

export const LinkContainer = styled(View)`
  
  width: 100%;
  
  justify-content: flex-end;
  gap: 24px;
  padding-horizontal: 24px;
  padding-vertical: 24px;
  border-color: ${({ theme }) => theme.colors.text};
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  background-color: ${({ theme }) => theme.colors.background};

  /* Refined shadow properties for iOS */
  shadow-color: ${({ theme }) => theme.colors.shadow};
  shadow-offset: 0px 2px;
  shadow-opacity: 0.08;
  shadow-radius: 6px;

  /* Refined shadow properties for Android */
  elevation: 3;
`;

export const LinkButton = styled(Pressable)`
  width: 100%;
  align-items: center;
  justify-content: center;
  padding: 12px;
`;

export const LinkText = styled(Text)`
  color: ${({ theme }) => theme.colors.lightBlue};
  font-weight: 600;
  font-size: 14px;
  text-align: center;
`;
export const ButtonContainer = styled(Button)`
  width: 100%;
`;

export const ButtonText = styled(Text)`
  font-size: 16px;
  font-weight: 700;
  color: ${({ theme }) => theme.colors.text};
  text-align: center;
`;