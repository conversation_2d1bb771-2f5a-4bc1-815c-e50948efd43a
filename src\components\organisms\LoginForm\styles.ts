import { styled } from "@/utils/styled";
import { View, Text } from "react-native";

export const Container = styled(View)`
  padding: 24px;
  background-color: ${({ theme }) => theme.colors.background};

  border-top-left-radius: 24px;
  border-top-right-radius: 24px;
`;

export const WelcomeText = styled(Text)`
  font-size: 24px;
  font-weight: 700;
  color: ${({ theme }) => theme.colors.text};
  text-align: center;
  margin-bottom: 32px;
`;

export const RegisterContainer = styled(View)`
  flex-direction: row;
  justify-content: center;
  align-items: center;
  margin-top: 24px;
  gap: 4px;
`;

export const RegisterText = styled(Text)`
  color: ${({ theme }) => theme.colors.text};
  font-size: 14px;
`;

export const RegisterLink = styled(Text)`
  color: ${({ theme }) => theme.colors.text};
  font-size: 14px;
  font-weight: 600;
`;
