import { ActivityIndicator } from "react-native";
import { styled } from "@/utils/styled";

export const ButtonContainer = styled.Pressable<{
  variant: "primary" | "secondary" | "outline";
  size: "small" | "medium" | "large";
}>`
  padding: ${({ size }) =>
    size === "small"
      ? "8px 16px"
      : size === "large"
        ? "16px 32px"
        : "12px 24px"};
  border-radius: 24px;
  background-color: ${({ theme, variant }) =>
    variant === "primary"
      ? theme.colors.primary
      : variant === "secondary"
        ? theme.colors.secondary
        : "transparent"};
  border: ${({ theme, variant }) =>
    variant === "outline" ? `1px solid ${theme.colors.primary}` : "none"};
`;

export const ButtonContent = styled.View`
  flex-direction: row;
  align-items: center;
  justify-content: center;
  
`;

export const ButtonText = styled.Text<{
  variant: "primary" | "secondary" | "outline";
  size: "small" | "medium" | "large";
}>`
  font-size: ${({ size }) =>
    size === "small" ? "14px" : size === "large" ? "18px" : "16px"};
  font-weight: 600;
  color: ${({ theme, variant, disabled }) =>
    disabled ? theme.colors.disabledButtonText : variant === "outline" ? theme.colors.primary : theme.colors.white};
`;

export const DisabledButton = styled.Pressable<{
  variant: "primary" | "secondary" | "outline";
  size: "small" | "medium" | "large";
}>`
  padding: ${({ size }) =>
    size === "small"
      ? "8px 16px"
      : size === "large"
        ? "16px 32px"
        : "12px 24px"};
  border-radius: 24px;
  background-color: ${({ theme }) => theme.colors.inputBackground};
  color: ${({ theme }) => theme.colors.text};
  border: none;
`;

export const StyledActivityIndicator = styled(ActivityIndicator)`
  margin-right: 8px;
`;
