import { FlatList, Pressable, TextInput, View } from "react-native";
import { styled } from "@/utils/styled";
import { BottomSheetView } from "@gorhom/bottom-sheet";
import { Image } from "expo-image";
import CrossIcon from "@/components/atoms/IllustratorIcon/CrossIcon";

export const Title = styled.Text`
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 12px;
  color: ${({ theme }) => theme.colors.text};
  text-align: center;
`;

export const SearchInputContainer = styled.View`
  flex-direction: row;
  align-items: center;
  height: 50px;
  border-radius: 8px;
  background-color: ${({ theme }) => theme.colors.inputBackground};
  padding-horizontal: 16px;
  border-width: 1px;
  border-color: ${({ theme }) => theme.colors.inputBackground};
`;

export const SearchInput = styled(TextInput)`
  flex: 1;
  height: 40px;
  color: ${({ theme }) => theme.colors.text};
  font-size: 16px;
  margin-left: 8px;
`;

export const ListContainer = styled(FlatList)``;

export const CountryItem = styled.Pressable`
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom-width: 1px;
  border-bottom-color: ${({ theme }) => theme.colors.lightGray};
`;

export const FlagContainer = styled.View`
  width: 32px;
  height: 24px;
  margin-right: 8px;
  justify-content: center;
  align-items: center;
`;

export const FlagPlaceholderImage = styled(Image)`
  width: 32px;
  height: 24px;
  border-radius: 4px;
  background-color: ${({ theme }) => theme.colors.gray};
`;

export const CountryName = styled.Text`
  font-size: 16px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text};
  margin-right: 8px;
`;

export const CountryCode = styled.Text`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.text};
`;

export const StyledBottomSheetView = styled(BottomSheetView)`
  padding-horizontal: 16px;

  padding-bottom: 16px;
`;

export const CountryItemContainer = styled(View)`
  padding-bottom: 10px;
  margin-bottom: 16px;
  margin-horizontal: -16px;
  position: relative;
  border-bottom-width: 1px;
  border-bottom-color: ${({ theme }) => theme.colors.border};
`;

export const CrossIconContainer = styled(Pressable)`
  position: absolute;
  right: 16px;
  top: 0;
  z-index: 999999;
`;

export const RenderListContainer = styled(View)`
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
`;