import React, { useState, useEffect } from "react";
import { useFormContext } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { useAppSelector } from "@/store/store";
import { CountryCode } from "@/types/auth";
import FormField from "../../molecules/FormField";
import Button from "../../atoms/Button";
import CountryCodePickerBottomSheet from "../CountryCodePickerBottomSheet";
import { Container, Form } from "./styles";
import { PhoneInput, SwitchField } from "@/components/molecules";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { useTheme } from "@/hooks/useTheme";

interface AddVendorBasicFormProps {
  onSubmit: () => void;
  loading?: boolean;
  countryCode: CountryCode | null;
  onCountrySelect: (country: CountryCode) => void;
}

export interface AddVendorBasicFormData {
  firstName: string;
  lastName: string;
  mobile: number;
  email: string;
  auth_dealer: boolean;
  gst_number: string;
}

const AddVendorBasicForm: React.FC<AddVendorBasicFormProps> = ({
  onSubmit,
  loading = false,
  countryCode,
  onCountrySelect,
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const {
    formState: { isValid },
    trigger,
    watch,
  } = useFormContext();
  const [showCountrySheet, setShowCountrySheet] = useState(false);
  const { countryList } = useAppSelector((state) => state.auth);

  // Watch form values to detect if form has data
  const formValues = watch();

  // Trigger validation when component mounts with existing data
  useEffect(() => {
    const hasExistingData =
      formValues.firstName ||
      formValues.lastName ||
      formValues.mobile ||
      formValues.email;

    if (hasExistingData) {
      // Trigger validation for all fields to update isValid state
      trigger();
    }
  }, []); // Only run on mount

  const handleCountrySelect = (item: CountryCode) => {
    onCountrySelect(item);
    setShowCountrySheet(false);
  };

  return (
    <Container>
      <KeyboardAwareScrollView showsVerticalScrollIndicator={false}>
        <Form>
          <FormField
            name="firstName"
            placeholder={t("first_name")}
            placeholderTextColor={theme.colors.gray}
            label={t("first_name")}
            rules={{ required: true }}
          />
          <FormField
            name="lastName"
            placeholder={t("last_name")}
            placeholderTextColor={theme.colors.gray}
            label={t("last_name")}
            rules={{ required: true }}
          />
          <FormField
            label={t("email")}
            name="email"
            placeholder={t("email")}
            placeholderTextColor={theme.colors.gray}
            keyboardType="email-address"
            autoCapitalize="none"
          />

          <PhoneInput
            label={t("phone_number")}
            name="mobile"
            rules={{ required: true }}
            placeholder={t("phone_number")}
            countryCode={countryCode}
            onCountrySelect={handleCountrySelect}
            countryList={countryList || []}
          />
        </Form>
        <FormField
          name="gst_number"
          label={t("gstin")}
          placeholder={t("enter_gst_number")}
          placeholderTextColor={theme.colors.gray}
          rules={{ required: true }}
          maxLength={15}
          autoCapitalize="characters"
        />
        <SwitchField
          name="auth_dealer"
          label={t("authorized_dealer")}
          color={theme.colors.text}
          size="medium"
        />
      </KeyboardAwareScrollView>

      <Button
        title={t("next")}
        onPress={onSubmit}
        loading={loading}
        disabled={!isValid || loading}
        style={{ marginTop: 20 }}
      />

      {showCountrySheet && (
        <CountryCodePickerBottomSheet
          isVisible={showCountrySheet}
          onClose={() => setShowCountrySheet(false)}
          onSelect={handleCountrySelect}
          data={countryList || []}
          title={t("select_country_code")}
          searchPlaceholder={t("search_country_code")}
          selectedCountry={countryCode}
        />
      )}
    </Container>
  );
};

export default AddVendorBasicForm;
