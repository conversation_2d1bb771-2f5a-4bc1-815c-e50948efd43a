import { styled } from "@/utils/styled";
import { View, Pressable } from "react-native";
import Input from "../../atoms/Input";

export const Container = styled(View)`
  margin-bottom: 16px;
  gap: 8px;
`;

export const InputRow = styled(View)`
  flex-direction: row;
  align-items: center;
  gap: 8px;
`;

export const CountryCodeButton = styled(Pressable)`
  flex-direction: row;
  align-items: center;
  padding: 16px 12px;
  border: 1px solid ${({ theme }) => theme.colors.inputBackground};
  border-radius: 8px;
  background-color: ${({ theme }) => theme.colors.inputBackground};
  height: 60px;
  margin-bottom: 8px;
`;

export const CountryCodeText = styled.Text`
  color: ${({ theme }) => theme.colors.text};
  font-size: 16px;
`;

export const PhoneInputField = styled(Input) <{ error?: boolean }>`
  flex: 1;
  padding: 16px;
  border: 1px solid
    ${({ theme, error }) => (error ? theme.colors.error : theme.colors.inputBackground)};
  border-radius: 8px;
  color: ${({ theme }) => theme.colors.inputColor};
  font-size: 16px;
`;
