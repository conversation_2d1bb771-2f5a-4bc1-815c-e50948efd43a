import { styled } from '@/utils/styled';

export const CardContainer = styled.Pressable`
  background-color: ${({ theme }) => theme.colors.card};
  border-radius: 12px;
  padding: 16px;
  margin: 0 16px 12px;
  flex-direction: row;
  shadow-color: ${({ theme }) => theme.colors.shadow};
  shadow-offset: 0px 2px;
  shadow-opacity: 0.1;
  shadow-radius: 4px;
`;

export const LeftSection = styled.View`
  flex: 1;
`;

export const RightSection = styled.View`
  align-items: flex-end;
  justify-content: space-between;
`;

export const TimeContainer = styled.View`
  flex-direction: row;
  align-items: center;
  background-color: ${({ theme }) => theme.colors.lightBlue};
  padding: 4px 8px;
  border-radius: 16px;
  align-self: flex-start;
  margin-bottom: 10px;
`;

export const TimeText = styled.Text`
  margin-left: 5px;
  color: ${({ theme }) => theme.colors.white};
  font-size: 12px;
  font-weight: 600;
`;

export const OrderId = styled.Text`
  font-size: 16px;
  font-weight: bold;
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 4px;
`;

export const Name = styled.Text`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 4px;
`;

export const DateText = styled.Text`
  font-size: 13px;
  color: ${({ theme }) => theme.colors.text};
`;

export const Amount = styled.Text`
  font-size: 18px;
  font-weight: bold;
  color: ${({ theme }) => theme.colors.primary};
`;