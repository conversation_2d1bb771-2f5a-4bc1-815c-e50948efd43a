import React from "react";
import { Image } from "react-native";
import { useRouter } from "expo-router";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { LoadingOverlay, LoginForm } from "@/components";
import { useTranslation } from "react-i18next";
import { loginOTPAction } from "@/store/actions/auth";
import { RootState, useAppDispatch, useAppSelector } from "@/store/store";
import { useTheme } from "@/hooks/useTheme";
import Toast from "react-native-toast-message";
import { setUser } from "@/store/slices/authSlice";
import FormTemplate from "@/template/FormTemplate";
import {
  TopContainer,
  StyledKeyboardAwareScrollView,
  StyledImage,
} from "@/styles/Login.styles";

const PHONE_REGEX = /^[0-9]{10}$/;

const createLoginSchema = (t: any) =>
  yup.object({
    phone: yup.string().matches(PHONE_REGEX, t("invalid_phone")),
  });

interface LoginFormData {
  phone: string;
}

export default function LoginScreen() {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { isCountryListLoading, isLoginLoading, user } = useAppSelector(
    (state: RootState) => state.auth
  );
  const { theme } = useTheme();
  const { t } = useTranslation();

  const loginSchema = createLoginSchema(t);

  const onSubmit = async (data: LoginFormData) => {
    try {
      const payload = {
        country_code_alpha: "IN", // Default for now
        mobile: data.phone,
      };
      const response = await dispatch(loginOTPAction(payload)).unwrap();
      dispatch(setUser(payload));
      Toast.show({
        type: "success",
        text1: response.message,
      });
      router.push("/otp");
    } catch (error) {
      Toast.show({
        type: "error",
        text1: error?.message,
      });
    }
  };

  const handleRegisterPress = () => {
    if (!!user?.role_id) {
      router.push("/register");
    } else {
      router.push("/intro");
    }
  };

  if (isCountryListLoading) {
    return <LoadingOverlay isLoading={isCountryListLoading} />;
  }
  return (
    <KeyboardAwareScrollView
      style={{ flex: 1, backgroundColor: theme.colors.card }}
      contentContainerStyle={{ flexGrow: 1 }}
      keyboardShouldPersistTaps="handled"
    >

      <TopContainer>
        <StyledImage
          source={require("../../assets/icon.png")}
          resizeMode="contain"
        />
      </TopContainer>

      <FormTemplate<LoginFormData>
        Component={(props) => (
          <LoginForm
            {...props}
            onRegisterPress={handleRegisterPress}
            loading={isLoginLoading}
          />
        )}
        onSubmit={onSubmit}
        defaultValues={{ phone: "" }}
        resolver={yupResolver(loginSchema)}
        mode="onChange"
      />
    </KeyboardAwareScrollView>
  );
}
