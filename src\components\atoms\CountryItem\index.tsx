import React from "react";
import { useTheme } from "@/hooks/useTheme";
import { PickerOption } from "@/components/molecules/PickerField/PickerBottomSheet";
import {
  Container,
  Flag,
  CountryName,
  Checkbox,
  CheckboxContainer,
} from "./styles";
import { RadioButton, RadioButtonInner } from "@/components/organisms/SortBottomSheet/styles";

interface CountryItemProps {
  item: PickerOption;
  isSelected?: boolean;
  displayKey?: string;
}

const CountryItem: React.FC<CountryItemProps> = ({
  item,
  isSelected,
  displayKey = "name",
}) => {
  const { theme } = useTheme();

  return (
    <Container>
      {item.flag && <Flag source={{ uri: item.flag }} contentFit="contain" />}
      <CountryName>{item[displayKey]}</CountryName>
      <CheckboxContainer>
        <RadioButton selected={isSelected}>
          <RadioButtonInner selected={isSelected} />
        </RadioButton>
      </CheckboxContainer>
    </Container>
  );
};

export default CountryItem;
