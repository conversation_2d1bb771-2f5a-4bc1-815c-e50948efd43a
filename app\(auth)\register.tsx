import React from "react";
import { Image } from "react-native";
import { useRouter } from "expo-router";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { LoadingOverlay, RegisterForm } from "@/components";
import { useTranslation } from "react-i18next";
import { RootState, useAppDispatch, useAppSelector } from "@/store/store";
import { registerOTPAction } from "@/store/actions/auth";
import { useTheme } from "@/hooks/useTheme";
import Toast from "react-native-toast-message";
import { updateUser } from "@/store/slices/authSlice";
import FormTemplate from "@/template/FormTemplate";
import { TopContainer, StyledImage } from "@/styles/Register.styles";

const NAME_REGEX = /^[a-zA-Z]{2,50}$/;
const PHONE_REGEX = /^[0-9]{10}$/;

const createRegisterSchema = (t: any) =>
  yup.object().shape({
    firstName: yup
      .string()
      .required(t("validation.first_name_required"))
      .transform((value) => value?.trim())
      .matches(NAME_REGEX, t("validation.first_name_format")),
    lastName: yup
      .string()
      .required(t("validation.last_name_required"))
      .transform((value) => value?.trim())
      .matches(NAME_REGEX, t("validation.last_name_format")),
    mobileNumber: yup
      .string()
      .required(t("validation.phone_required"))
      .matches(PHONE_REGEX, t("invalid_phone")),
  });

interface RegisterFormData {
  firstName: string;
  lastName: string;
  mobileNumber: string;
}

export default function RegisterScreen() {
  const router = useRouter();
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { isRegisterLoading, user, isCountryListLoading } = useAppSelector(
    (state: RootState) => state.auth
  );
  const { theme } = useTheme();

  const registerSchema = createRegisterSchema(t);

  const onSubmit = async (data: RegisterFormData) => {
    try {
      const payload = {
        ...user,
        country_code_alpha: "IN", // Default for now
        mobile: data.mobileNumber,
        first_name: data.firstName.trim(),
        last_name: data.lastName.trim(),
      };
      const response = await dispatch(registerOTPAction(payload)).unwrap();
      dispatch(updateUser(payload));
      Toast.show({
        type: "success",
        text1: response.message,
      });
      router.push("/otp");
    } catch (error) {
      Toast.show({
        type: "error",
        text1: error?.message,
      });
    }
  };

  const handleLoginPress = () => {
    router.push("/login");
  };

  if (isCountryListLoading) {
    return <LoadingOverlay isLoading={isCountryListLoading} />;
  }

  return (
    <KeyboardAwareScrollView
      style={{ flex: 1, backgroundColor: theme.colors.card }}
      contentContainerStyle={{ flexGrow: 1 }}
      keyboardShouldPersistTaps="handled"
    >
      <TopContainer>
        <StyledImage
          source={require("../../assets/icon.png")}
          resizeMode="contain"

        />
      </TopContainer>

      <FormTemplate<RegisterFormData>
        Component={(props) => (
          <RegisterForm
            {...props}
            onLoginPress={handleLoginPress}
            loading={isRegisterLoading}
          />
        )}
        onSubmit={onSubmit}
        defaultValues={{
          firstName: "",
          lastName: "",
          mobileNumber: "",
        }}
        resolver={yupResolver(registerSchema)}
        mode="onChange"
      />
    </KeyboardAwareScrollView>
  );
}
