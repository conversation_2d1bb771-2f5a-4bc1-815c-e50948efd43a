import React, {
  use<PERSON><PERSON>back,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { BottomSheetModal, BottomSheetFlatList } from "@gorhom/bottom-sheet";
import { Ionicons } from "@expo/vector-icons";
import LoadingOverlay from "@/components/atoms/LoadingOverlay";
import { useTheme } from "@/hooks/useTheme";
import {
  Title,
  SearchContainer,
  SearchInput,
  PickerItem,
  PickerItemText,
  EmptyContainer,
  EmptyText,
  LoadingContainer,
  ItemsCountText,
  StyledBottomSheetView,
} from "./PickerBottomSheetStyles";
import { useBottomSheetBackdrop } from "@/utils/commonBackdrop";
import { BackHandler } from "react-native";
import { CountryItemContainer, CrossIconContainer, SearchInputContainer } from "@/components/organisms/CountryCodePickerBottomSheet/styles";
import CrossIcon from "@/components/atoms/IllustratorIcon/CrossIcon";
import SearchIcon from "@/components/atoms/IllustratorIcon/SearchIcon";
import { RadioButton, RadioButtonInner } from "@/components/organisms/SortBottomSheet/styles";

export interface PickerOption {
  id?: number | string;
  [key: string]: any;
}

interface PickerBottomSheetProps {
  isVisible: boolean;
  onClose: () => void;
  onSelect: (item: PickerOption) => Promise<void>;
  data: PickerOption[];
  title?: string;
  searchPlaceholder?: string;
  displayKey?: string;
  isLoading?: boolean;
  searchable?: boolean;
  renderItem?: (item: PickerOption) => React.ReactNode;
  backgroundStyle?: string;
  // Pagination props
  hasPagination?: boolean;
  currentPage?: number;
  lastPage?: number;
  onLoadMore?: () => void;
  isLoadingMore?: boolean;
  itemsCountText?: string;
  // Search props
  onSearch?: (query: string) => void;
  searchValue?: string;
  onSearchChange?: (value: string) => void;
  // Multiple selection props
  isMultiple?: boolean;
  selectedItems?: (string | number)[];
}

const PickerBottomSheet: React.FC<PickerBottomSheetProps> = ({
  isVisible,
  onClose,
  onSelect,
  data,
  title = "Select Option",
  searchPlaceholder = "Search options",
  displayKey = "name",
  isLoading = false,
  searchable = true,
  renderItem,
  backgroundStyle,
  // Pagination props
  hasPagination = false,
  currentPage = 1,
  lastPage = 1,
  onLoadMore,
  isLoadingMore = false,
  itemsCountText,
  // Search props
  onSearch,
  searchValue = "",
  onSearchChange,
  // Multiple selection props
  isMultiple = false,
  selectedItems = [],
}) => {
  const sheetRef = useRef<BottomSheetModal>(null);
  const [query, setQuery] = useState(searchValue);
  const [filtered, setFiltered] = useState<PickerOption[]>(data);
  const { theme } = useTheme();

  useEffect(() => {
    if (isVisible) {
      sheetRef.current?.present();
      const onBackPress = () => {
        onClose();
        return true;
      };
      BackHandler.addEventListener("hardwareBackPress", onBackPress);
      return () => {
        BackHandler.removeEventListener("hardwareBackPress", onBackPress);
      };
    } else {
      sheetRef.current?.dismiss();
    }
  }, [isVisible]);

  // Handle local filtering if no external search
  useEffect(() => {
    if (!onSearch) {
      if (!query || !searchable) {
        setFiltered(data);
        return;
      }
      setFiltered(
        data.filter((item) =>
          item[displayKey]
            ?.toString()
            .toLowerCase()
            .includes(query.toLowerCase())
        )
      );
    }
  }, [query, data, displayKey, searchable, onSearch]);

  // Sync with external search value
  useEffect(() => {
    if (onSearchChange) {
      setQuery(searchValue);
    }
  }, [searchValue, onSearchChange]);

  const handleDismiss = useCallback(() => {
    onClose?.();
  }, [onClose]);

  const snapPoints = useMemo(() => ["30%", "50%", "75%"], []);

  const handleSheetChanges = useCallback(
    (index: number) => {
      if (index === -1) {
        onClose?.();
      }
    },
    [onClose]
  );

  const handleSearch = useCallback(
    (text: string) => {
      setQuery(text);
      if (onSearchChange) {
        onSearchChange(text);
      }
    },
    [onSearchChange]
  );

  const handleLoadMore = useCallback(() => {
    if (
      hasPagination &&
      onLoadMore &&
      currentPage < lastPage &&
      !isLoadingMore
    ) {
      onLoadMore();
    }
  }, [hasPagination, onLoadMore, currentPage, lastPage, isLoadingMore]);

  const renderPickerItem = useCallback(
    ({ item }: { item: PickerOption }) => {
      const isSelected =
        isMultiple && selectedItems.includes(item.id || item.part_id);

      if (renderItem) {
        return (
          <PickerItem
            onPress={async () => {
              await onSelect(item);
            }}
            style={{
              backgroundColor: isSelected
                ? theme.colors.primary + "20"
                : "transparent",
            }}
          >
            {renderItem(item)}
            {isSelected && (
              <RadioButton selected={isSelected}>
                <RadioButtonInner selected={isSelected} />
              </RadioButton>
            )}
          </PickerItem>
        );
      }

      return (
        <PickerItem
          onPress={async () => {
            await onSelect(item);
          }}
          style={{
            backgroundColor: isSelected
              ? theme.colors.primary + "20"
              : "transparent",
          }}
        >
          <PickerItemText>{item[displayKey]}</PickerItemText>
          {isSelected && (
            <RadioButton selected={isSelected}>
              <RadioButtonInner selected={isSelected} />
            </RadioButton>
          )}
        </PickerItem>
      );
    },
    [onSelect, displayKey, renderItem, isMultiple, selectedItems, theme.colors]
  );

  const renderEmptyComponent = useCallback(() => {
    if (isLoading) {
      return (
        <LoadingContainer>
          <LoadingOverlay isLoading={isLoading} size="large" />
        </LoadingContainer>
      );
    }

    return (
      <EmptyContainer>
        <EmptyText>
          {query ? "No results found" : "No options available"}
        </EmptyText>
      </EmptyContainer>
    );
  }, [isLoading, query]);

  const renderFooter = useCallback(() => {
    if (hasPagination && isLoadingMore) {
      return (
        <LoadingContainer>
          <LoadingOverlay isLoading={true} size="small" />
        </LoadingContainer>
      );
    }
    return null;
  }, [hasPagination, isLoadingMore]);

  const renderBackdrop = useBottomSheetBackdrop();

  return (
    <BottomSheetModal
      ref={sheetRef}
      snapPoints={snapPoints}
      onDismiss={handleDismiss}
      onChange={handleSheetChanges}
      index={1}
      style={{ borderTopLeftRadius: 20, borderTopRightRadius: 20 }}
      backdropComponent={renderBackdrop}
      enableDynamicSizing={false}
      enablePanDownToClose
      backgroundStyle={{ backgroundColor: theme.colors.background }}
      handleIndicatorStyle={{ backgroundColor: theme.colors.gray }}
    >
      <StyledBottomSheetView>
        <CountryItemContainer>
          <Title>{title}</Title>
          <CrossIconContainer onPress={onClose}>
            <CrossIcon color={theme.colors.text} />
          </CrossIconContainer>
        </CountryItemContainer>
        {searchable && (
          <SearchInputContainer>
            <SearchIcon color={theme.colors.inputColor} />
            <SearchInput
              placeholder={searchPlaceholder}
              value={query}
              onChangeText={handleSearch}
              placeholderTextColor={theme.colors.inputColor}
              style={{ color: theme.colors.inputColor }}
            />
          </SearchInputContainer>
        )}

        {itemsCountText && <ItemsCountText>{itemsCountText}</ItemsCountText>}

        <BottomSheetFlatList
          data={onSearch ? data : filtered}
          keyExtractor={(item: PickerOption) =>
            item.id?.toString() || item.name
          }
          renderItem={renderPickerItem}
          ListEmptyComponent={renderEmptyComponent}
          ListFooterComponent={renderFooter}
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.5}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
          contentContainerStyle={{
            paddingBottom: 20,
            flexGrow: data.length === 0 ? 1 : undefined,
          }}
        />
      </StyledBottomSheetView>
    </BottomSheetModal>
  );
};

export default PickerBottomSheet;
