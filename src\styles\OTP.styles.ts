import { styled } from "../utils/styled";
import Button from "../components/atoms/Button";

export const Container = styled.View`
  flex: 1;
  background-color: ${({ theme }) => theme.colors.card};
`;

export const TopContainer = styled.View`
  flex: 1;
  align-items: center;
  justify-content: flex-end;
  min-height: 260px;

`;

export const BottomCard = styled.View`
  width: 100%;
  background-color: ${({ theme }) => theme.colors.white};
  border-top-left-radius: 32px;
  border-top-right-radius: 32px;
  padding-horizontal: 24px;
  padding-top: 40px;
  padding-bottom: 32px;
  align-items: center;
  shadow-color: ${({ theme }) => theme.colors.shadow};
  shadow-offset: 0px -2px;
  shadow-opacity: 0.06;
  shadow-radius: 8px;
  elevation: 8;
  margin-top: -24px;
`;

export const OTPTitle = styled.Text`
  font-size: 28px;
  font-weight: 700;
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 24px;
  text-align: center;
`;

export const OTPContainer = styled.View`
  flex-direction: row;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 24px;
`;

export const OTPInput = styled.TextInput<{ focused?: boolean }>`
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background-color: ${({ theme }) => theme.colors.inputBackground};
  text-align: center;
  font-size: 20px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text};
  border-width: 1px;
  border-color: ${({ focused, theme }) =>
    focused ? theme.colors.secondary : "transparent"};
`;

export const ResendRow = styled.View`
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
`;

export const ResendText = styled.Text`
  color: ${({ theme }) => theme.colors.gray};
  font-size: 14px;
`;

export const ResendLink = styled.Text`
  color: ${({ theme }) => theme.colors.link};
  font-weight: 600;
  font-size: 14px;
`;

export const TimerText = styled.Text`
  color: ${({ theme }) => theme.colors.gray};
  font-size: 14px;
`;

export const VerifyButton = styled(Button)`
  background-color: ${({ theme }) => theme.colors.secondary};
  border-radius: 12px;
  padding-vertical: 16px;
  align-items: center;
  width: 100%;
  margin-top: 8px;
`;

export const BackButton = styled.Pressable`
  margin-top: 16px;
  border-width: 1px;
  width: 100%;
  align-items: center;
  justify-content: center;
  border-color: ${({ theme }) => theme.colors.lightGray};
  border-radius: 12px;
  padding-vertical: 12px;
  margin-top: 16px;
`;

export const BackButtonText = styled.Text`
  color: ${({ theme }) => theme.colors.gray};
  font-size: 14px;
`;
