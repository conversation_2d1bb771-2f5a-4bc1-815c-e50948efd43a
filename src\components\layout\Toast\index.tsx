import React from "react";
import { BaseToastProps } from "react-native-toast-message";
import { useTheme } from "@/hooks/useTheme";
import { useTranslation } from "react-i18next";
import { Ionicons } from "@expo/vector-icons";
import { ToastContainer, ToastText, IconContainer } from "./styles";

const CustomErrorToast = (props: BaseToastProps) => {
  const { theme } = useTheme();
  const { t } = useTranslation();

  const text1 =
    props.text1 && typeof props.text1 === "string" && props.text1.trim()
      ? props.text1
      : t("common.error_message");

  return (
    <ToastContainer type="error">
      <IconContainer>
        <Ionicons
          name="close-circle"
          size={24}
          color={theme.colors.error}
        />
      </IconContainer>
      <ToastText numberOfLines={3}>
        {text1}
      </ToastText>
    </ToastContainer>
  );
};

const CustomSuccessToast = (props: BaseToastProps) => {
  const { theme } = useTheme();

  return (
    <ToastContainer type="success">
      <IconContainer>
        <Ionicons
          name="checkmark-circle"
          size={24}
          color={theme.colors.success}
        />
      </IconContainer>
      <ToastText numberOfLines={3}>
        {props.text1}
      </ToastText>
    </ToastContainer>
  );
};

const CustomInfoToast = (props: BaseToastProps) => {
  const { theme } = useTheme();

  return (
    <ToastContainer type="info">
      <IconContainer>
        <Ionicons
          name="information-circle"
          size={24}
          color={theme.colors.info}
        />
      </IconContainer>
      <ToastText numberOfLines={3}>
        {props.text1}
      </ToastText>
    </ToastContainer>
  );
};

export const toastConfig = {
  success: (props: any) => <CustomSuccessToast {...props} />,
  error: (props: any) => <CustomErrorToast {...props} />,
  info: (props: any) => <CustomInfoToast {...props} />,
};
