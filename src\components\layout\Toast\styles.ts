import { styled } from "@/utils/styled";
import { View, Text } from "react-native";

export const ToastContainer = styled(View)<{
  type: "success" | "error" | "info";
}>`
  margin: 20px;
  padding: 16px 20px;
  border-radius: 12px;
  flex-direction: row;
  align-items: center;
  background-color: ${(props) => {
    switch (props.type) {
      case "success":
        return ({ theme }) => theme.colors.success;
      case "error":
        return ({ theme }) => theme.colors.error;
      default:
        return ({ theme }) => theme.colors.info;
    }
  }};
  elevation: 8;
  shadow-color: ${({ theme }) => theme.colors.black};
  shadow-offset: 0px 4px;
  shadow-opacity: 0.3;
  shadow-radius: 8px;
  min-height: 60px;
`;

export const ToastText = styled(Text)`
  color: ${({ theme }) => theme.colors.white};
  font-size: 16px;
  font-weight: 500;
  flex: 1;
  margin-left: 16px;
  line-height: 22px;
`;

export const IconContainer = styled(View)`
  width: 40px;
  height: 40px;
  border-radius: 20px;
  justify-content: center;
  align-items: center;
  background-color: ${({ theme }) => theme.colors.white};
  shadow-color: ${({ theme }) => theme.colors.black};
  shadow-offset: 0px 2px;
  shadow-opacity: 0.1;
  shadow-radius: 4px;
  elevation: 2;
`;
